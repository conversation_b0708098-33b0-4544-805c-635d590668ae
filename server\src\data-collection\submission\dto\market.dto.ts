import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsOptional, IsString, IsEnum, IsBoolean, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { MarketCategory, MarketOpeningDays, WaterAvailability, WaterAvailabilityFrequency, CleanWaterStorageCapacity, MainWaterSource, WaterSourceDistance, ToiletFacilityType, FacilitySlabConstructionMaterial, ExcretaManagement, HandWashingFacilityType, HandWashingMaterial, MarketHandWashingMaterial, WasteManagementAfterSeparation, WasteTreatmentType, WasteCollectionFrequency, WasteWaterManagement } from '@prisma/client';
import { BaseCreateSubmissionDto, BaseSubmissionResponseDto } from './base-submission.dto';

export class MarketGeneralInfoDto {
  @ApiProperty()
  @IsString()
  marketName: string;

  @ApiProperty({ enum: MarketCategory })
  @IsEnum(MarketCategory)
  marketCategory: MarketCategory;

  @ApiProperty({ enum: MarketOpeningDays })
  @IsEnum(MarketOpeningDays)
  openingDays: MarketOpeningDays;
}

export class MarketWaterSupplyDto {
  @ApiProperty()
  @IsBoolean()
  connectedToPipeline: boolean;

  @ApiProperty({ enum: WaterAvailability })
  @IsEnum(WaterAvailability)
  waterAvailability: WaterAvailability;

  @ApiProperty({ enum: WaterAvailabilityFrequency, required: false })
  @IsOptional()
  @IsEnum(WaterAvailabilityFrequency)
  availableDays?: WaterAvailabilityFrequency;

  @ApiProperty({ enum: CleanWaterStorageCapacity })
  @IsEnum(CleanWaterStorageCapacity)
  storageCapacity: CleanWaterStorageCapacity;

  @ApiProperty({ enum: MainWaterSource, required: false })
  @IsOptional()
  @IsEnum(MainWaterSource)
  mainWaterSource?: MainWaterSource;

  @ApiProperty({ enum: WaterSourceDistance })
  @IsEnum(WaterSourceDistance)
  distanceToSource: WaterSourceDistance;
}

export class MarketSanitationDto {
  @ApiProperty({ enum: ToiletFacilityType })
  @IsEnum(ToiletFacilityType)
  toiletType: ToiletFacilityType;

  @ApiProperty({ enum: FacilitySlabConstructionMaterial })
  @IsEnum(FacilitySlabConstructionMaterial)
  slabConstructionMaterial: FacilitySlabConstructionMaterial;

  @ApiProperty()
  @IsInt()
  totalToilets: number;

  @ApiProperty()
  @IsBoolean()
  genderSeparation: boolean;

  @ApiProperty()
  @IsInt()
  femaleToilets: number;

  @ApiProperty()
  @IsInt()
  maleToilets: number;

  @ApiProperty()
  @IsBoolean()
  girlsRoom: boolean;

  @ApiProperty()
  @IsBoolean()
  disabilityAccess: boolean;

  @ApiProperty()
  @IsBoolean()
  staffToilets: boolean;

  @ApiProperty()
  @IsBoolean()
  hasToiletFullInLast2Years: boolean;

  @ApiProperty({ enum: ExcretaManagement, required: false })
  @IsOptional()
  @IsEnum(ExcretaManagement)
  excretaManagement?: ExcretaManagement;
}

export class MarketHygieneDto {
  @ApiProperty()
  @IsBoolean()
  handwashingFacility: boolean;

  @ApiProperty({ enum: HandWashingFacilityType, required: false })
  @IsOptional()
  @IsEnum(HandWashingFacilityType)
  facilityType?: HandWashingFacilityType;

  @ApiProperty({ enum: MarketHandWashingMaterial, required: false })
  @IsOptional()
  @IsEnum(MarketHandWashingMaterial)
  handwashingMaterials?: MarketHandWashingMaterial;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  handWashingfacilityNearToilet?: boolean;

  @ApiProperty({ enum: HandWashingFacilityType, required: false })
  @IsOptional()
  @IsEnum(HandWashingFacilityType)
  toiletHandWashingFacilityType?: HandWashingFacilityType;

  @ApiProperty({ enum: HandWashingMaterial, required: false })
  @IsOptional()
  @IsEnum(HandWashingMaterial)
  toiletHandWashingMaterials?: HandWashingMaterial;
}

export class MarketSolidWasteManagementDto {
  @ApiProperty()
  @IsBoolean()
  wasteSeparation: boolean;

  @ApiProperty({ enum: WasteManagementAfterSeparation, required: false })
  @IsOptional()
  @IsEnum(WasteManagementAfterSeparation)
  wasteManagement?: WasteManagementAfterSeparation;

  @ApiProperty({ enum: WasteTreatmentType, required: false })
  @IsOptional()
  @IsEnum(WasteTreatmentType)
  treatmentType?: WasteTreatmentType;

  @ApiProperty({ enum: WasteCollectionFrequency, required: false })
  @IsOptional()
  @IsEnum(WasteCollectionFrequency)
  collectionFrequency?: WasteCollectionFrequency;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsInt()
  collectionCost?: number;
}

export class MarketLiquidWasteManagementDto {
  @ApiProperty({ enum: WasteWaterManagement })
  @IsEnum(WasteWaterManagement)
  liquidWasteManagement: WasteWaterManagement;
}

export class CreateMarketSubmissionDto extends BaseCreateSubmissionDto {
  @ApiProperty({ type: MarketGeneralInfoDto })
  @ValidateNested()
  @Type(() => MarketGeneralInfoDto)
  generalInfo: MarketGeneralInfoDto;

  @ApiProperty({ type: MarketWaterSupplyDto })
  @ValidateNested()
  @Type(() => MarketWaterSupplyDto)
  waterSupply: MarketWaterSupplyDto;

  @ApiProperty({ type: MarketSanitationDto })
  @ValidateNested()
  @Type(() => MarketSanitationDto)
  sanitation: MarketSanitationDto;

  @ApiProperty({ type: MarketHygieneDto })
  @ValidateNested()
  @Type(() => MarketHygieneDto)
  hygiene: MarketHygieneDto;

  @ApiProperty({ type: MarketSolidWasteManagementDto })
  @ValidateNested()
  @Type(() => MarketSolidWasteManagementDto)
  solidWaste: MarketSolidWasteManagementDto;

  @ApiProperty({ type: MarketLiquidWasteManagementDto })
  @ValidateNested()
  @Type(() => MarketLiquidWasteManagementDto)
  liquidWaste: MarketLiquidWasteManagementDto;
}

export class MarketSubmissionResponseDto extends BaseSubmissionResponseDto {
  @ApiProperty({ type: MarketGeneralInfoDto })
  generalInfo: MarketGeneralInfoDto;

  @ApiProperty({ type: MarketWaterSupplyDto })
  waterSupply: MarketWaterSupplyDto;

  @ApiProperty({ type: MarketSanitationDto })
  sanitation: MarketSanitationDto;

  @ApiProperty({ type: MarketHygieneDto })
  hygiene: MarketHygieneDto;

  @ApiProperty({ type: MarketSolidWasteManagementDto })
  solidWaste: MarketSolidWasteManagementDto;

  @ApiProperty({ type: MarketLiquidWasteManagementDto })
  liquidWaste: MarketLiquidWasteManagementDto;
}
