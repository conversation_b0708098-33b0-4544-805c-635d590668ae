import { SubmissionService } from './submission.service';
import { CreateHouseholdSubmissionDto } from './dto/household.dto';
import { CreateSchoolSubmissionDto } from './dto/school.dto';
import { CreateHealthFacilitySubmissionDto } from './dto/health-facility.dto';
export declare class SubmissionController {
    private readonly submissionService;
    constructor(submissionService: SubmissionService);
    createHousehold(dto: CreateHouseholdSubmissionDto, user: any): Promise<import("./dto/base-submission.dto").BaseCreateSubmissionResponseDto>;
    createSchool(dto: CreateSchoolSubmissionDto, user: any): Promise<{
        message: string;
        submission: {
            id: string;
            facilityType: import("@prisma/client").$Enums.FacilityType;
            submittedAt: Date;
            submittedById: string;
            houseHoldId: string | null;
            schoolId: string | null;
            healthFacilityId: string | null;
            publicPlaceId: string | null;
        };
    }>;
    createHealthFacility(dto: CreateHealthFacilitySubmissionDto, user: any): Promise<{
        message: string;
        submission: {
            id: string;
            facilityType: import("@prisma/client").$Enums.FacilityType;
            submittedAt: Date;
            submittedById: string;
            houseHoldId: string | null;
            schoolId: string | null;
            healthFacilityId: string | null;
            publicPlaceId: string | null;
        };
    }>;
}
