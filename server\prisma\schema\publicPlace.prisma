model PublicPlace {
  id         String   @id @default(uuid())
  location   Location @relation(fields: [locationId], references: [id])
  locationId String
  name       String   
  number     String      @unique
  type       PublicPlaceType
  deleted Boolean @default(false)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  Submission Submission[]
}

model PublicPlaceGeneralInfo {
  id           String     @id @default(uuid())
  submissionId String
  submission   Submission @relation(fields: [submissionId], references: [id])

  name     String
  category MarketCategory
  openingDays MarketOpeningDays
}

model PublicPlaceWaterSupply {
  id           String     @id @default(uuid())
  submissionId String
  submission   Submission @relation(fields: [submissionId], references: [id])

  connectedToPipeline Boolean
  waterAvailability   WaterAvailability
  availableDays       WaterAvailabilityFrequency?
  storageCapacity     CleanWaterStorageCapacity
  mainWaterSource     MainWaterSource?
  distanceToSource    WaterSourceDistance
}

model PublicPlaceSanitation {
  id           String     @id @default(uuid())
  submissionId String
  submission   Submission @relation(fields: [submissionId], references: [id])

  toiletType                ToiletFacilityType
  slabConstructionMaterial  FacilitySlabConstructionMaterial
  totalToilets              Int
  genderSeparation          Boolean
  femaleToilets             Int
  maleToilets               Int
  girlsRoom                 Boolean
  disabilityAccess          Boolean
  staffToilets              Boolean
  hasToiletFullInLast2Years Boolean
  excretaManagement         ExcretaManagement?
}

model PublicPlaceHygiene {
  id           String     @id @default(uuid())
  submissionId String
  submission   Submission @relation(fields: [submissionId], references: [id])

  handwashingFacility           Boolean
  facilityType                  HandWashingFacilityType?
  handwashingMaterials          MarketHandWashingMaterial?
  handWashingfacilityNearToilet Boolean?
  toiletHandWashingFacilityType HandWashingFacilityType?
  toiletHandWashingMaterials    HandWashingMaterial?
}

model PublicPlaceSolidWasteManagement {
  id           String     @id @default(uuid())
  submissionId String
  submission   Submission @relation(fields: [submissionId], references: [id])

  wasteSeparation     Boolean
  wasteManagement     WasteManagementAfterSeparation?
  treatmentType       WasteTreatmentType?
  collectionFrequency WasteCollectionFrequency?
  collectionCost      Int?
}

model PublicPlaceLiquidWasteManagement {
  id           String     @id @default(uuid())
  submissionId String
  submission   Submission @relation(fields: [submissionId], references: [id])

  liquidWasteManagement WasteWaterManagement
}
