generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["multiSchema"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Location {
  id        String  @id @default(uuid())
  villageId Int
  village   Village @relation(fields: [villageId], references: [id])

  latitude  Float?
  longitude Float?

  settlementType SettlementType @default(RURAL)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  houseHolds       HouseHold[]
  schools          School[]
  healthFacilities HealthFacility[]
  publicPlaces     PublicPlace[]
}

enum FacilityType {
  HOUSEHOLD
  SCHOOL
  HEALTH_FACILITY
  MARKET
  WASTE_COLLECTION_COMPANY
  WASTE_RECOVERY_COMPANY
  WASTE_DISPOSAL_COMPANY
}

model Submission {
  id            String       @id @default(uuid())
  facilityType  FacilityType
  submittedById String
  submittedBy   User         @relation(fields: [submittedById], references: [id])
  submittedAt   DateTime     @default(now())

  houseHoldId String?
  household   HouseHold? @relation(fields: [houseHoldId], references: [id])

  schoolId String?
  school   School? @relation(fields: [schoolId], references: [id])

  healthFacilityId String?
  healthFacility   HealthFacility? @relation(fields: [healthFacilityId], references: [id])

  publicPlaceId String?
  publicPlace   PublicPlace? @relation(fields: [publicPlaceId], references: [id])

  HouseHoldGeneralInfo HouseHoldGeneralInfo[]

  HouseHoldWaterSupply HouseHoldWaterSupply[]

  HouseHoldSanitation HouseHoldSanitation[]

  HouseHoldHygiene HouseHoldHygiene[]

  HouseHoldSolidWasteManagement HouseHoldSolidWasteManagement[]

  HouseHoldLiquidWasteManagement HouseHoldLiquidWasteManagement[]

  HealthFacilitySanitation HealthFacilitySanitation[]

  HealthFacilityHygiene HealthFacilityHygiene[]

  HealthFacilitySolidWasteManagement HealthFacilitySolidWasteManagement[]

  HealthFacilityLiquidWasteManagement HealthFacilityLiquidWasteManagement[]

  HealthFacilityGeneralInfo HealthFacilityGeneralInfo[]

  HealthFacilityWaterSupply HealthFacilityWaterSupply[]

  PublicPlaceGeneralInfo PublicPlaceGeneralInfo[]
  PublicPlaceWaterSupply PublicPlaceWaterSupply[]
  PublicPlaceSanitation PublicPlaceSanitation[]
  PublicPlaceHygiene PublicPlaceHygiene[]
  PublicPlaceSolidWasteManagement PublicPlaceSolidWasteManagement[]
  PublicPlaceLiquidWasteManagement PublicPlaceLiquidWasteManagement[]

  SchoolSolidWasteManagement SchoolSolidWasteManagement[]

  SchoolLiquidWasteManagement SchoolLiquidWasteManagement[]

  SchoolGeneralInfo SchoolGeneralInfo[]

  SchoolWaterSupply SchoolWaterSupply[]

  SchoolSanitation SchoolSanitation[]

  SchoolHygiene SchoolHygiene[]

  WasteRecoveryCompany WasteRecoveryCompany[]

  WasteCollectionCompany WasteCollectionCompany[]

  WasteDisposalCompany WasteDisposalCompany[]
}
