import { FacilitiesService } from './facilities.service';
import { PaginationQueryDto, BaseDeleteResponseDto } from './dto/base-facility.dto';
import { CreateHouseholdDto, UpdateHouseholdDto, HouseholdResponseDto, HouseholdsListResponseDto, CreateHouseholdResponseDto, UpdateHouseholdResponseDto } from './dto/household.dto';
import { CreateSchoolDto, UpdateSchoolDto, SchoolResponseDto, SchoolsListResponseDto, CreateSchoolResponseDto, UpdateSchoolResponseDto } from './dto/school.dto';
import { CreateHealthFacilityDto, UpdateHealthFacilityDto, HealthFacilityResponseDto, HealthFacilitiesListResponseDto, CreateHealthFacilityResponseDto, UpdateHealthFacilityResponseDto } from './dto/health-facility.dto';
export declare class FacilitiesController {
    private readonly facilitiesService;
    constructor(facilitiesService: FacilitiesService);
    createHousehold(createHouseholdDto: CreateHouseholdDto): Promise<CreateHouseholdResponseDto>;
    findAllHouseholds(query: PaginationQueryDto): Promise<HouseholdsListResponseDto>;
    findOneHousehold(id: string): Promise<HouseholdResponseDto>;
    updateHousehold(id: string, updateHouseholdDto: UpdateHouseholdDto): Promise<UpdateHouseholdResponseDto>;
    removeHousehold(id: string): Promise<BaseDeleteResponseDto>;
    createSchool(createSchoolDto: CreateSchoolDto): Promise<CreateSchoolResponseDto>;
    findAllSchools(query: PaginationQueryDto): Promise<SchoolsListResponseDto>;
    findOneSchool(id: string): Promise<SchoolResponseDto>;
    updateSchool(id: string, updateSchoolDto: UpdateSchoolDto): Promise<UpdateSchoolResponseDto>;
    removeSchool(id: string): Promise<BaseDeleteResponseDto>;
    createHealthFacility(createHealthFacilityDto: CreateHealthFacilityDto): Promise<CreateHealthFacilityResponseDto>;
    findAllHealthFacilities(query: PaginationQueryDto): Promise<HealthFacilitiesListResponseDto>;
    findOneHealthFacility(id: string): Promise<HealthFacilityResponseDto>;
    updateHealthFacility(id: string, updateHealthFacilityDto: UpdateHealthFacilityDto): Promise<UpdateHealthFacilityResponseDto>;
    removeHealthFacility(id: string): Promise<BaseDeleteResponseDto>;
}
